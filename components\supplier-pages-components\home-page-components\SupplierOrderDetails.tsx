import { useLocalSearchPara<PERSON>, use<PERSON><PERSON><PERSON>, <PERSON>ack } from 'expo-router';
import { ScrollView, Dimensions } from 'react-native';
import {
  Text,
  View,
  YStack,
  XStack,
  H2,
  H3,
  <PERSON><PERSON>tor,
  <PERSON><PERSON>,
  <PERSON>,
} from 'tamagui';
import { useMyOrdersStore } from '~/components/customer-pages-components/orders-page-components/useMyOrdersStore';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { LinearGradient } from 'expo-linear-gradient';
import { useState } from 'react';

export default function SupplierOrderDetails() {
  const { orderId } = useLocalSearchParams<{ orderId: string }>();
  const router = useRouter();
  const order = useMyOrdersStore((s) => s.orders.find((o) => o.id === orderId));
  const [isUpdating, setIsUpdating] = useState(false);

  const windowWidth = Dimensions.get('window').width;

  if (!order) {
    return (
      <View flex={1} ai="center" jc="center" bg="$gray1">
        <MotiView
          from={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ type: 'spring', damping: 15 }}
        >
          <YStack ai="center" gap="$4" p="$6">
            <View
              style={{
                backgroundColor: '#f3f4f6',
                borderRadius: 20,
                padding: 20,
              }}
            >
              <Ionicons name="document-text-outline" size={48} color="#6b7280" />
            </View>
            <H3 color="$gray11">Order Not Found</H3>
            <Text color="$gray9" textAlign="center">
              The order you're looking for doesn't exist or has been removed.
            </Text>
            <Button
              bg="$primary"
              color="white"
              br="$6"
              onPress={() => router.back()}
              icon={<Ionicons name="arrow-back" size={20} color="white" />}
            >
              Go Back
            </Button>
          </YStack>
        </MotiView>
      </View>
    );
  }

  const handleMarkDelivered = async () => {
    setIsUpdating(true);
    try {
      useMyOrdersStore.setState((state) => ({
        orders: state.orders.map((o) =>
          o.id === order.id ? { ...o, status: 'Delivered', supplierRecievedMoney: true } : o
        ),
      }));

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Navigate back safely
      if (router.canGoBack()) {
        router.back();
      } else {
        router.replace('/(supplier-pages)/home');
      }
    } catch (error) {
      console.error('Error updating order to Delivered:', error);
    } finally {
      setIsUpdating(false);
    }
  }

  const handleMarkPreparing = async () => {
    setIsUpdating(true);
    try {
      useMyOrdersStore.setState((state) => ({
        orders: state.orders.map((o) =>
          o.id === order.id ? { ...o, status: 'Preparing' } : o
        ),
      }));

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Navigate back safely
      if (router.canGoBack()) {
        router.back();
      } else {
        router.replace('/(supplier-pages)/home');
      }
    } catch (error) {
      console.error('Error updating order to Preparing:', error);
    } finally {
      setIsUpdating(false);
    }
  }

  const handleMarkOnTheWay = async () => {
    setIsUpdating(true);
    try {
      useMyOrdersStore.setState((state) => ({
        orders: state.orders.map((o) =>
          o.id === order.id ? { ...o, status: 'On the Way' } : o
        ),
      }));

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Navigate back safely
      if (router.canGoBack()) {
        router.back();
      } else {
        router.replace('/(supplier-pages)/home');
      }
    } catch (error) {
      console.error('Error updating order to On the Way:', error);
    } finally {
      setIsUpdating(false);
    }
  }

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'Pending':
        return {
          color: '#ef4444',
          bgColor: ['#ef4444', '#dc2626'],
          icon: 'alert-circle',
          label: 'New Order'
        };
      case 'Preparing':
        return {
          color: '#f59e0b',
          bgColor: ['#f59e0b', '#d97706'],
          icon: 'timer',
          label: 'Preparing'
        };
      case 'On the Way':
        return {
          color: '#f97316',
          bgColor: ['#f97316', '#ea580c'],
          icon: 'car',
          label: 'On the Way'
        };
      case 'Delivered':
        return {
          color: '#10b981',
          bgColor: ['#10b981', '#059669'],
          icon: 'checkmark-done',
          label: 'Delivered'
        };
      default:
        return {
          color: '#6b7280',
          bgColor: ['#6b7280', '#4b5563'],
          icon: 'help',
          label: status
        };
    }
  };

  const statusConfig = getStatusConfig(order.status);

  return (
    <>
      <Stack.Screen options={{ title: `Order #${orderId}`, headerShown: true }} />

      <ScrollView
        contentContainerStyle={{ padding: 16, paddingBottom: 120, width: windowWidth }}
        showsVerticalScrollIndicator={false}
        style={{ backgroundColor: '#f8fafc' }}
      >
        <YStack gap="$5">
          {/* EXTREME Header */}
          <MotiView
            from={{ opacity: 0, translateY: -50, scale: 0.9 }}
            animate={{ opacity: 1, translateY: 0, scale: 1 }}
            transition={{ type: 'spring', damping: 20, stiffness: 300 }}
          >
            <Card
              elevate
              br="$10"
              overflow="hidden"
              borderWidth={0}
              shadowColor={statusConfig.color}
              shadowOffset={{ width: 0, height: 16 }}
              shadowOpacity={0.4}
              shadowRadius={32}
            >
              <LinearGradient
                colors={statusConfig.bgColor as any}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={{ padding: 32 }}
              >
                {/* Decorative Background Elements */}
                <View
                  style={{
                    position: 'absolute',
                    top: -60,
                    right: -60,
                    width: 140,
                    height: 140,
                    borderRadius: 70,
                    backgroundColor: 'rgba(255,255,255,0.1)',
                  }}
                />
                <View
                  style={{
                    position: 'absolute',
                    bottom: -40,
                    left: -40,
                    width: 100,
                    height: 100,
                    borderRadius: 50,
                    backgroundColor: 'rgba(255,255,255,0.08)',
                  }}
                />

                <YStack gap="$4">
                  {/* Order Header */}
                  <XStack ai="center" gap="$4">
                    <MotiView
                      from={{ scale: 0, rotate: '-180deg' }}
                      animate={{ scale: 1, rotate: '0deg' }}
                      transition={{ delay: 400, type: 'spring', damping: 15 }}
                    >
                      <View style={{ position: 'relative' }}>
                        <LinearGradient
                          colors={['rgba(255,255,255,0.3)', 'rgba(255,255,255,0.1)']}
                          style={{
                            borderRadius: 24,
                            padding: 20,
                            borderWidth: 2,
                            borderColor: 'rgba(255,255,255,0.2)',
                          }}
                        >
                          <Ionicons name={statusConfig.icon as any} size={40} color="white" />
                        </LinearGradient>
                      </View>
                    </MotiView>

                    <YStack flex={1} gap="$2">
                      <MotiView
                        from={{ opacity: 0, translateX: -20 }}
                        animate={{ opacity: 1, translateX: 0 }}
                        transition={{ delay: 600, duration: 600 }}
                      >
                        <H2 color="white" fontWeight="900" fontSize="$8">
                          Order #{order.id}
                        </H2>
                      </MotiView>

                      <MotiView
                        from={{ opacity: 0, translateX: -20 }}
                        animate={{ opacity: 1, translateX: 0 }}
                        transition={{ delay: 700, duration: 600 }}
                      >
                        <XStack ai="center" gap="$3">
                          <View
                            style={{
                              backgroundColor: 'rgba(255,255,255,0.2)',
                              borderRadius: 8,
                              paddingHorizontal: 12,
                              paddingVertical: 6,
                            }}
                          >
                            <Text color="white" fontSize="$2" fontWeight="700">
                              {statusConfig.label.toUpperCase()}
                            </Text>
                          </View>
                          <Text color="white" opacity={0.9} fontSize="$3" fontWeight="500">
                            {new Date(order.createdAt).toLocaleDateString()}
                          </Text>
                        </XStack>
                      </MotiView>
                    </YStack>
                  </XStack>

                  {/* Order Stats */}
                  <MotiView
                    from={{ opacity: 0, translateY: 20 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ delay: 800, duration: 600 }}
                  >
                    <XStack gap="$4" jc="space-between">
                      <YStack ai="center" gap="$1" flex={1}>
                        <Text color="white" opacity={0.8} fontSize="$2" fontWeight="600">
                          TOTAL
                        </Text>
                        <Text color="white" fontSize="$6" fontWeight="900">
                          ₪{order.total.toFixed(0)}
                        </Text>
                      </YStack>

                      <View
                        style={{
                          width: 1,
                          backgroundColor: 'rgba(255,255,255,0.3)',
                        }}
                      />

                      <YStack ai="center" gap="$1" flex={1}>
                        <Text color="white" opacity={0.8} fontSize="$2" fontWeight="600">
                          ITEMS
                        </Text>
                        <Text color="white" fontSize="$6" fontWeight="900">
                          {order.items.reduce((sum, item) => sum + item.qty, 0)}
                        </Text>
                      </YStack>

                      <View
                        style={{
                          width: 1,
                          backgroundColor: 'rgba(255,255,255,0.3)',
                        }}
                      />

                      <YStack ai="center" gap="$1" flex={1}>
                        <Text color="white" opacity={0.8} fontSize="$2" fontWeight="600">
                          PAYMENT
                        </Text>
                        <Text color="white" fontSize="$6" fontWeight="900">
                          {order.paymentMethod.toUpperCase()}
                        </Text>
                      </YStack>
                    </XStack>
                  </MotiView>
                </YStack>
              </LinearGradient>
            </Card>
          </MotiView>

          {/* Enhanced Customer Info */}
          <MotiView
            from={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 200, duration: 500 }}
          >
            <Card
              elevate
              p="$5"
              br="$8"
              bg="white"
              borderWidth={1}
              borderColor="$gray4"
              shadowColor="$gray6"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
            >
              <YStack gap="$4">
                <XStack ai="center" gap="$3">
                  <LinearGradient
                    colors={['#3b82f6', '#2563eb']}
                    style={{
                      borderRadius: 12,
                      padding: 12,
                    }}
                  >
                    <Ionicons name="person" size={24} color="white" />
                  </LinearGradient>
                  <H3 color="$gray12" fontWeight="800">Customer Details</H3>
                </XStack>

                <YStack gap="$3">
                  <XStack ai="center" gap="$3">
                    <View
                      style={{
                        backgroundColor: '#f3f4f6',
                        borderRadius: 8,
                        padding: 8,
                      }}
                    >
                      <Ionicons name="location" size={16} color="#6b7280" />
                    </View>
                    <YStack flex={1}>
                      <Text color="$gray8" fontSize="$2" fontWeight="600">ADDRESS</Text>
                      <Text color="$gray12" fontSize="$3" fontWeight="500">
                        {order.address?.address || 'No address provided'}
                      </Text>
                    </YStack>
                  </XStack>

                  <XStack ai="center" gap="$3">
                    <View
                      style={{
                        backgroundColor: '#f3f4f6',
                        borderRadius: 8,
                        padding: 8,
                      }}
                    >
                      <Ionicons name="call" size={16} color="#6b7280" />
                    </View>
                    <YStack flex={1}>
                      <Text color="$gray8" fontSize="$2" fontWeight="600">PHONE</Text>
                      <Text color="$gray12" fontSize="$3" fontWeight="500">
                        {order.phone}
                      </Text>
                    </YStack>
                  </XStack>

                  {order.note ? (
                    <XStack ai="flex-start" gap="$3">
                      <View
                        style={{
                          backgroundColor: '#f3f4f6',
                          borderRadius: 8,
                          padding: 8,
                        }}
                      >
                        <Ionicons name="chatbubble" size={16} color="#6b7280" />
                      </View>
                      <YStack flex={1}>
                        <Text color="$gray8" fontSize="$2" fontWeight="600">NOTE</Text>
                        <Text color="$gray12" fontSize="$3" fontWeight="500">
                          {order.note}
                        </Text>
                      </YStack>
                    </XStack>
                  ) : null}
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Enhanced Order Items */}
          <MotiView
            from={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 300, duration: 500 }}
          >
            <Card
              elevate
              p="$5"
              br="$8"
              bg="white"
              borderWidth={1}
              borderColor="$gray4"
              shadowColor="$gray6"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
            >
              <YStack gap="$4">
                <XStack ai="center" gap="$3">
                  <LinearGradient
                    colors={['#f59e0b', '#d97706']}
                    style={{
                      borderRadius: 12,
                      padding: 12,
                    }}
                  >
                    <Ionicons name="restaurant" size={24} color="white" />
                  </LinearGradient>
                  <H3 color="$gray12" fontWeight="800">Order Items</H3>
                </XStack>

                <YStack gap="$3">
                  {order.items.map((item, idx) => (
                    <MotiView
                      key={idx}
                      from={{ opacity: 0, translateX: -20 }}
                      animate={{ opacity: 1, translateX: 0 }}
                      transition={{ delay: 400 + idx * 100, duration: 400 }}
                    >
                      <Card
                        bg="$gray1"
                        p="$4"
                        br="$6"
                        borderWidth={1}
                        borderColor="$gray3"
                      >
                        <XStack ai="center" gap="$3">
                          <View
                            style={{
                              backgroundColor: '#f97316',
                              borderRadius: 8,
                              padding: 8,
                              minWidth: 40,
                              alignItems: 'center',
                            }}
                          >
                            <Text color="white" fontSize="$3" fontWeight="800">
                              {item.qty}×
                            </Text>
                          </View>

                          <YStack flex={1} gap="$1">
                            <Text color="$gray12" fontSize="$4" fontWeight="700">
                              {item.product.name}
                            </Text>
                            <Text color="$gray9" fontSize="$2">
                              {item.product.category}
                            </Text>
                          </YStack>

                          <YStack ai="flex-end" gap="$1">
                            <Text color="$gray12" fontSize="$4" fontWeight="800">
                              ₪{item.finalPrice.toFixed(2)}
                            </Text>
                            <Text color="$gray8" fontSize="$2">
                              ₪{(item.finalPrice / item.qty).toFixed(2)} each
                            </Text>
                          </YStack>
                        </XStack>
                      </Card>
                    </MotiView>
                  ))}
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Enhanced Payment Summary */}
          <MotiView
            from={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 400, duration: 500 }}
          >
            <Card
              elevate
              p="$5"
              br="$8"
              bg="white"
              borderWidth={1}
              borderColor="$gray4"
              shadowColor="$gray6"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
            >
              <YStack gap="$4">
                <XStack ai="center" gap="$3">
                  <LinearGradient
                    colors={['#10b981', '#059669']}
                    style={{
                      borderRadius: 12,
                      padding: 12,
                    }}
                  >
                    <Ionicons name="card" size={24} color="white" />
                  </LinearGradient>
                  <H3 color="$gray12" fontWeight="800">Payment Summary</H3>
                </XStack>

                <YStack gap="$3">
                  <XStack jc="space-between" ai="center">
                    <Text color="$gray10" fontSize="$3">Subtotal</Text>
                    <Text color="$gray12" fontSize="$4" fontWeight="600">
                      ₪{order.subTotal.toFixed(2)}
                    </Text>
                  </XStack>

                  <XStack jc="space-between" ai="center">
                    <Text color="$gray10" fontSize="$3">Delivery Fee</Text>
                    <Text color="$gray12" fontSize="$4" fontWeight="600">
                      ₪{order.deliveryFee.toFixed(2)}
                    </Text>
                  </XStack>

                  {order.promo ? (
                    <XStack jc="space-between" ai="center">
                      <Text color="$green9" fontSize="$3">Promo ({order.promo})</Text>
                      <Text color="$green9" fontSize="$4" fontWeight="600">
                        -₪5.00
                      </Text>
                    </XStack>
                  ) : null}

                  <Separator />

                  <XStack jc="space-between" ai="center">
                    <Text color="$gray12" fontSize="$5" fontWeight="800">Total</Text>
                    <Text color="$gray12" fontSize="$6" fontWeight="900">
                      ₪{order.total.toFixed(2)}
                    </Text>
                  </XStack>

                  <XStack jc="space-between" ai="center">
                    <Text color="$gray8" fontSize="$2">Payment Method</Text>
                    <View
                      style={{
                        backgroundColor: order.paymentMethod === 'cash' ? '#f59e0b' : '#3b82f6',
                        borderRadius: 6,
                        paddingHorizontal: 8,
                        paddingVertical: 4,
                      }}
                    >
                      <Text color="white" fontSize="$2" fontWeight="600">
                        {order.paymentMethod.toUpperCase()}
                      </Text>
                    </View>
                  </XStack>
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Enhanced Action Buttons */}
          <MotiView
            from={{ opacity: 0, translateY: 30 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 500, duration: 600 }}
          >
            <YStack gap="$3">
              {order.status === 'Pending' ? (
                <Button
                  size="$5"
                  br="$8"
                  bg="transparent"
                  borderWidth={0}
                  pressStyle={{ scale: 0.98 }}
                  onPress={handleMarkPreparing}
                  disabled={isUpdating}
                >
                  <LinearGradient
                    colors={['#10b981', '#059669']}
                    style={{
                      borderRadius: 16,
                      padding: 15,
                      width: '100%',
                      alignItems: 'center',
                      flexDirection: 'row',
                      justifyContent: 'center',
                      gap: 12,
                    }}
                  >
                    <Ionicons name="checkmark-circle" size={24} color="white" />
                    <Text color="white" fontSize="$5" fontWeight="800">
                      {isUpdating ? 'Accepting...' : 'Accept & Start Preparing'}
                    </Text>
                  </LinearGradient>
                </Button>
              ) : null}

              {order.status === 'Preparing' ? (
                <Button
                  size="$5"
                  br="$8"
                  bg="transparent"
                  borderWidth={0}
                  pressStyle={{ scale: 0.98 }}
                  onPress={handleMarkOnTheWay}
                  disabled={isUpdating}
                >
                  <LinearGradient
                    colors={['#f97316', '#ea580c']}
                    style={{
                      borderRadius: 16,
                      padding: 15,
                      width: '100%',
                      alignItems: 'center',
                      flexDirection: 'row',
                      justifyContent: 'center',
                      gap: 12,
                    }}
                  >
                    <Ionicons name="car" size={24} color="white" />
                    <Text color="white" fontSize="$5" fontWeight="800">
                      {isUpdating ? 'Updating...' : 'Mark On The Way'}
                    </Text>
                  </LinearGradient>
                </Button>
              ) : null}

              {order.status === 'On the Way' ? (
                <>
                  <Button
                    size="$5"
                    br="$8"
                    bg="transparent"
                    borderWidth={0}
                    pressStyle={{ scale: 0.98 }}
                    onPress={() => router.push({
                      pathname: '/(supplier-pages)/home/<USER>',
                      params: { orderId: orderId }
                    })}
                  >
                    <LinearGradient
                      colors={['#7c3aed', '#6d28d9']}
                      style={{
                        borderRadius: 16,
                        padding: 15,
                        width: '100%',
                        alignItems: 'center',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        gap: 12,
                      }}
                    >
                      <Ionicons name="location" size={24} color="white" />
                      <Text color="white" fontSize="$5" fontWeight="800">
                        Track Delivery
                      </Text>
                    </LinearGradient>
                  </Button>

                  <Button
                    size="$5"
                    br="$8"
                    bg="transparent"
                    borderWidth={0}
                    pressStyle={{ scale: 0.98 }}
                    onPress={handleMarkDelivered}
                    disabled={isUpdating}
                  >
                    <LinearGradient
                      colors={['#10b981', '#059669']}
                      style={{
                        borderRadius: 16,
                        padding: 15,
                        width: '100%',
                        alignItems: 'center',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        gap: 12,
                      }}
                    >
                      <Ionicons name="checkmark-done" size={24} color="white" />
                      <Text color="white" fontSize="$5" fontWeight="800">
                        {isUpdating ? 'Marking...' : 'Mark as Delivered'}
                      </Text>
                    </LinearGradient>
                  </Button>
                </>
              ) : null}

              {order.status === 'Delivered' ? (
                <Card
                  p="$4"
                  br="$8"
                  bg="$green1"
                  borderWidth={2}
                  borderColor="$green6"
                >
                  <XStack ai="center" jc="center" gap="$3">
                    <Ionicons name="checkmark-done-circle" size={32} color="#10b981" />
                    <YStack ai="center">
                      <Text color="$green11" fontSize="$5" fontWeight="800">
                        Order Completed! 🎉
                      </Text>
                      <Text color="$green9" fontSize="$3">
                        {order.paymentMethod === 'cash' && order.supplierRecievedMoney
                          ? 'Payment received'
                          : order.paymentMethod === 'card'
                          ? 'Payment processed'
                          : 'Awaiting payment confirmation'
                        }
                      </Text>
                    </YStack>
                  </XStack>
                </Card>
              ) : null}
            </YStack>
          </MotiView>
        </YStack>
      </ScrollView>
    </>
  )
}
