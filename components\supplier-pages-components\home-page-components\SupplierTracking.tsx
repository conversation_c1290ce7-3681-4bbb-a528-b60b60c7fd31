import { useEffect, useRef, useState } from 'react';
import { ScrollView, Dimensions, Pressable, Modal, Linking } from 'react-native';
import { useLocalSearchParams, Stack } from 'expo-router';
import { Text, YStack, Card, H2, H3, H4, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ner, Button, View } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { useMyOrdersStore } from '~/components/customer-pages-components/orders-page-components/useMyOrdersStore';
import MapView, { <PERSON><PERSON>, <PERSON>yl<PERSON> } from 'react-native-maps';
import { MotiView } from 'moti';
import { LinearGradient } from 'expo-linear-gradient';

export default function SupplierTracking() {
  const { orderId } = useLocalSearchParams<{ orderId: string }>();
  const order = useMyOrdersStore((s) => s.orders.find((o) => o.id === orderId));

  const [simulatedDriver, setSimulatedDriver] = useState(order?.driverLocation ?? null);
  const [mapFullscreen, setMapFullscreen] = useState(false);
  const [estimatedTime, setEstimatedTime] = useState(12);
  const [isLiveTracking, setIsLiveTracking] = useState(true);
  const cameraRef = useRef(null);

  const windowWidth = Dimensions.get('window').width;

  const dropoff = order?.address?.lng && order?.address?.lat
    ? [order.address.lng, order.address.lat]
    : [35.2544, 32.2211]; // Nablus, Palestine

  const driverCoord: [number, number] =
    simulatedDriver && typeof simulatedDriver.lng === 'number' && typeof simulatedDriver.lat === 'number'
      ? [simulatedDriver.lng, simulatedDriver.lat]
      : (dropoff as [number, number]);

  // Enhanced driver simulation with realistic movement
  useEffect(() => {
    if (!simulatedDriver || !isLiveTracking) return;

    const interval = setInterval(() => {
      setSimulatedDriver((prev) => {
        if (!prev) return null;

        // Calculate distance to destination
        const distance = Math.sqrt(
          Math.pow(dropoff[0] - prev.lng, 2) + Math.pow(dropoff[1] - prev.lat, 2)
        );

        // If very close to destination, stop moving
        if (distance < 0.0001) {
          setIsLiveTracking(false);
          setEstimatedTime(0);
          return prev;
        }

        // Move towards destination with some randomness
        const moveSpeed = 0.00008;
        const randomFactor = 0.00002;

        return {
          lng: prev.lng + (Math.random() - 0.5) * randomFactor + moveSpeed,
          lat: prev.lat + (Math.random() - 0.5) * randomFactor + moveSpeed * 0.6,
          address: 'Moving towards destination...'
        };
      });

      // Update estimated time
      setEstimatedTime(prev => Math.max(0, prev - 0.5));
    }, 3000);

    return () => clearInterval(interval);
  }, [simulatedDriver, isLiveTracking, dropoff]);

  if (!order) {
    return (
      <View flex={1} ai="center" jc="center" bg="$gray1">
        <MotiView
          from={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ type: 'spring', damping: 15 }}
        >
          <YStack ai="center" gap="$4" p="$6">
            <View
              style={{
                backgroundColor: '#f3f4f6',
                borderRadius: 20,
                padding: 20,
              }}
            >
              <Ionicons name="location-outline" size={48} color="#6b7280" />
            </View>
            <H3 color="$gray11">Order Not Found</H3>
            <Text color="$gray9" textAlign="center">
              Unable to track this order. It may have been completed or cancelled.
            </Text>
          </YStack>
        </MotiView>
      </View>
    );
  }

  if (!simulatedDriver) {
    return (
      <View flex={1} ai="center" jc="center" bg="$gray1">
        <MotiView
          from={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ type: 'spring', damping: 15 }}
        >
          <YStack ai="center" gap="$4" p="$6">
            <Spinner size="large" color="$primary" />
            <H4 color="$gray11">Initializing Tracking...</H4>
            <Text color="$gray9" textAlign="center">
              Setting up real-time location tracking for your order.
            </Text>
          </YStack>
        </MotiView>
      </View>
    );
  }

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'Preparing':
        return {
          color: '#f59e0b',
          bgColor: ['#f59e0b', '#d97706'],
          icon: 'timer',
          label: 'Preparing Order'
        };
      case 'On the Way':
        return {
          color: '#f97316',
          bgColor: ['#f97316', '#ea580c'],
          icon: 'car',
          label: 'On the Way'
        };
      case 'Delivered':
        return {
          color: '#10b981',
          bgColor: ['#10b981', '#059669'],
          icon: 'checkmark-done',
          label: 'Delivered'
        };
      default:
        return {
          color: '#6b7280',
          bgColor: ['#6b7280', '#4b5563'],
          icon: 'help',
          label: status
        };
    }
  };

  const statusConfig = getStatusConfig(order.status);

  return (
    <>
      <Stack.Screen options={{ title: `Tracking #${orderId}`, headerShown: true }} />

      {/* Enhanced Fullscreen Map Modal */}
      <Modal visible={mapFullscreen} animationType="slide">
        <View style={{ flex: 1 }}>
          <MapView
            style={{ flex: 1 }}
            initialRegion={{
              latitude: dropoff[1],
              longitude: dropoff[0],
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            }}
            showsUserLocation={true}
            showsMyLocationButton={true}
            showsCompass={true}
            showsScale={true}
            showsTraffic={true}
            mapType="standard"
          >
            {/* Driver Marker */}
            <Marker
              coordinate={{
                latitude: driverCoord[1],
                longitude: driverCoord[0],
              }}
              title={`Driver: ${order.driverName}`}
              description={`ETA: ${estimatedTime} mins`}
            >
              <View
                style={{
                  backgroundColor: '#f97316',
                  borderRadius: 20,
                  padding: 8,
                  borderWidth: 3,
                  borderColor: 'white',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.3,
                  shadowRadius: 4,
                }}
              >
                <Ionicons name="car" size={24} color="white" />
              </View>
            </Marker>

            {/* Destination Marker */}
            <Marker
              coordinate={{
                latitude: dropoff[1],
                longitude: dropoff[0],
              }}
              title="Delivery Destination"
              description={order.address?.address}
            >
              <View
                style={{
                  backgroundColor: '#10b981',
                  borderRadius: 20,
                  padding: 8,
                  borderWidth: 3,
                  borderColor: 'white',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.3,
                  shadowRadius: 4,
                }}
              >
                <Ionicons name="location" size={24} color="white" />
              </View>
            </Marker>

            {/* Route Line */}
            <Polyline
              coordinates={[
                { latitude: driverCoord[1], longitude: driverCoord[0] },
                { latitude: dropoff[1], longitude: dropoff[0] }
              ]}
              strokeColor="#7c3aed"
              strokeWidth={4}
              lineDashPattern={[10, 5]}
            />
          </MapView>

          {/* Enhanced Map Controls */}
          <View
            style={{
              position: 'absolute',
              top: 50,
              left: 20,
              right: 20,
              zIndex: 1000,
            }}
          >
            <Card
              bg="rgba(255,255,255,0.95)"
              p="$3"
              br="$8"
              shadowColor="$gray8"
              shadowOffset={{ width: 0, height: 4 }}
              shadowOpacity={0.2}
              shadowRadius={8}
            >
              <XStack ai="center" jc="space-between">
                <XStack ai="center" gap="$2">
                  <View
                    style={{
                      backgroundColor: isLiveTracking ? '#10b981' : '#6b7280',
                      borderRadius: 6,
                      width: 12,
                      height: 12,
                    }}
                  />
                  <Text fontSize="$3" fontWeight="600" color="$gray12">
                    {isLiveTracking ? 'Live Tracking' : 'Tracking Paused'}
                  </Text>
                </XStack>

                <Button
                  size="$3"
                  circular
                  bg="$primary"
                  color="white"
                  icon={<Ionicons name="close" size={20} color="white" />}
                  onPress={() => setMapFullscreen(false)}
                  pressStyle={{ scale: 0.95 }}
                />
              </XStack>
            </Card>
          </View>

          {/* Driver Info Overlay */}
          <View
            style={{
              position: 'absolute',
              bottom: 30,
              left: 20,
              right: 20,
              zIndex: 1000,
            }}
          >
            <Card
              bg="rgba(255,255,255,0.95)"
              p="$4"
              br="$8"
              shadowColor="$gray8"
              shadowOffset={{ width: 0, height: 4 }}
              shadowOpacity={0.2}
              shadowRadius={8}
            >
              <XStack ai="center" gap="$3">
                <LinearGradient
                  colors={statusConfig.bgColor as any}
                  style={{
                    borderRadius: 12,
                    padding: 12,
                  }}
                >
                  <Ionicons name="car" size={24} color="white" />
                </LinearGradient>

                <YStack flex={1} gap="$1">
                  <Text fontSize="$4" fontWeight="800" color="$gray12">
                    {order.driverName}
                  </Text>
                  <Text fontSize="$2" color="$gray9">
                    ETA: {estimatedTime > 0 ? `${estimatedTime} minutes` : 'Arriving now!'}
                  </Text>
                </YStack>

                <Button
                  size="$3"
                  circular
                  bg="$green9"
                  color="white"
                  icon={<Ionicons name="call" size={18} color="white" />}
                  onPress={() => Linking.openURL(`tel:${order.driverPhone}`)}
                  pressStyle={{ scale: 0.95 }}
                />
              </XStack>
            </Card>
          </View>
        </View>
      </Modal>

      <ScrollView
        contentContainerStyle={{ padding: 16, paddingBottom: 120, width: windowWidth }}
        showsVerticalScrollIndicator={false}
        style={{ backgroundColor: '#f8fafc' }}
      >
        <YStack gap="$5">
          {/* EXTREME Tracking Header */}
          <MotiView
            from={{ opacity: 0, translateY: -50, scale: 0.9 }}
            animate={{ opacity: 1, translateY: 0, scale: 1 }}
            transition={{ type: 'spring', damping: 20, stiffness: 300 }}
          >
            <Card
              elevate
              br="$10"
              overflow="hidden"
              borderWidth={0}
              shadowColor={statusConfig.color}
              shadowOffset={{ width: 0, height: 16 }}
              shadowOpacity={0.4}
              shadowRadius={32}
            >
              <LinearGradient
                colors={statusConfig.bgColor as any}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={{ padding: 32 }}
              >
                {/* Decorative Background Elements */}
                <View
                  style={{
                    position: 'absolute',
                    top: -60,
                    right: -60,
                    width: 140,
                    height: 140,
                    borderRadius: 70,
                    backgroundColor: 'rgba(255,255,255,0.1)',
                  }}
                />
                <View
                  style={{
                    position: 'absolute',
                    bottom: -40,
                    left: -40,
                    width: 100,
                    height: 100,
                    borderRadius: 50,
                    backgroundColor: 'rgba(255,255,255,0.08)',
                  }}
                />

                <YStack gap="$4">
                  {/* Tracking Header */}
                  <XStack ai="center" gap="$4">
                    <MotiView
                      from={{ scale: 0, rotate: '-180deg' }}
                      animate={{ scale: 1, rotate: '0deg' }}
                      transition={{ delay: 400, type: 'spring', damping: 15 }}
                    >
                      <View style={{ position: 'relative' }}>
                        <LinearGradient
                          colors={['rgba(255,255,255,0.3)', 'rgba(255,255,255,0.1)']}
                          style={{
                            borderRadius: 24,
                            padding: 20,
                            borderWidth: 2,
                            borderColor: 'rgba(255,255,255,0.2)',
                          }}
                        >
                          <Ionicons name="location" size={40} color="white" />
                        </LinearGradient>

                        {/* Live Tracking Indicator */}
                        {isLiveTracking ? (
                          <MotiView
                            from={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 600, type: 'spring', damping: 10 }}
                            style={{
                              position: 'absolute',
                              top: -8,
                              right: -8,
                            }}
                          >
                            <View
                              style={{
                                backgroundColor: '#10b981',
                                borderRadius: 12,
                                padding: 6,
                                borderWidth: 2,
                                borderColor: 'white',
                              }}
                            >
                              <View
                                style={{
                                  width: 8,
                                  height: 8,
                                  borderRadius: 4,
                                  backgroundColor: 'white',
                                }}
                              />
                            </View>
                          </MotiView>
                        ) : null}
                      </View>
                    </MotiView>

                    <YStack flex={1} gap="$2">
                      <MotiView
                        from={{ opacity: 0, translateX: -20 }}
                        animate={{ opacity: 1, translateX: 0 }}
                        transition={{ delay: 600, duration: 600 }}
                      >
                        <H2 color="white" fontWeight="900" fontSize="$8">
                          🚚 Live Tracking
                        </H2>
                      </MotiView>

                      <MotiView
                        from={{ opacity: 0, translateX: -20 }}
                        animate={{ opacity: 1, translateX: 0 }}
                        transition={{ delay: 700, duration: 600 }}
                      >
                        <XStack ai="center" gap="$3">
                          <View
                            style={{
                              backgroundColor: 'rgba(255,255,255,0.2)',
                              borderRadius: 8,
                              paddingHorizontal: 12,
                              paddingVertical: 6,
                            }}
                          >
                            <Text color="white" fontSize="$2" fontWeight="700">
                              ORDER #{order.id}
                            </Text>
                          </View>
                          <Text color="white" opacity={0.9} fontSize="$3" fontWeight="500">
                            {statusConfig.label}
                          </Text>
                        </XStack>
                      </MotiView>
                    </YStack>
                  </XStack>

                  {/* ETA and Driver Info */}
                  <MotiView
                    from={{ opacity: 0, translateY: 20 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ delay: 800, duration: 600 }}
                  >
                    <XStack gap="$4" jc="space-between">
                      <YStack ai="center" gap="$1" flex={1}>
                        <Text color="white" opacity={0.8} fontSize="$2" fontWeight="600">
                          ETA
                        </Text>
                        <Text color="white" fontSize="$6" fontWeight="900">
                          {estimatedTime > 0 ? `${estimatedTime}m` : 'NOW!'}
                        </Text>
                      </YStack>

                      <View
                        style={{
                          width: 1,
                          backgroundColor: 'rgba(255,255,255,0.3)',
                        }}
                      />

                      <YStack ai="center" gap="$1" flex={1}>
                        <Text color="white" opacity={0.8} fontSize="$2" fontWeight="600">
                          DRIVER
                        </Text>
                        <Text color="white" fontSize="$4" fontWeight="800" textAlign="center">
                          {order.driverName}
                        </Text>
                      </YStack>

                      <View
                        style={{
                          width: 1,
                          backgroundColor: 'rgba(255,255,255,0.3)',
                        }}
                      />

                      <YStack ai="center" gap="$1" flex={1}>
                        <Text color="white" opacity={0.8} fontSize="$2" fontWeight="600">
                          STATUS
                        </Text>
                        <Text color="white" fontSize="$4" fontWeight="800" textAlign="center">
                          {isLiveTracking ? 'LIVE' : 'PAUSED'}
                        </Text>
                      </YStack>
                    </XStack>
                  </MotiView>
                </YStack>
              </LinearGradient>
            </Card>
          </MotiView>

          {/* Enhanced Map Preview */}
          <MotiView
            from={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 200, duration: 500 }}
          >
            <Card
              elevate
              br="$8"
              overflow="hidden"
              borderWidth={0}
              shadowColor="$gray6"
              shadowOffset={{ width: 0, height: 12 }}
              shadowOpacity={0.2}
              shadowRadius={24}
            >
              <LinearGradient
                colors={['#7c3aed', '#6d28d9']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={{ padding: 20 }}
              >
                <XStack ai="center" jc="space-between" mb="$3">
                  <XStack ai="center" gap="$3">
                    <View
                      style={{
                        backgroundColor: 'rgba(255,255,255,0.2)',
                        borderRadius: 12,
                        padding: 10,
                      }}
                    >
                      <Ionicons name="map" size={24} color="white" />
                    </View>
                    <YStack>
                      <H3 color="white" fontWeight="800">
                        Live Map View
                      </H3>
                      <Text color="white" opacity={0.9} fontSize="$3">
                        Tap to open full tracking
                      </Text>
                    </YStack>
                  </XStack>

                  <Button
                    size="$3"
                    circular
                    bg="rgba(255,255,255,0.2)"
                    borderColor="rgba(255,255,255,0.3)"
                    borderWidth={2}
                    icon={<Ionicons name="expand" size={18} color="white" />}
                    onPress={() => setMapFullscreen(true)}
                    pressStyle={{ scale: 0.95 }}
                  />
                </XStack>
              </LinearGradient>

              <Pressable onPress={() => setMapFullscreen(true)}>
                <View bg="white" p="$4" height={200} jc="center" ai="center">
                  <YStack gap="$4" ai="center">
                    <XStack gap="$6" ai="center">
                      <YStack ai="center" gap="$2">
                        <View
                          style={{
                            backgroundColor: '#f97316',
                            borderRadius: 16,
                            padding: 12,
                            borderWidth: 2,
                            borderColor: 'white',
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.2,
                            shadowRadius: 4,
                          }}
                        >
                          <Ionicons name="car" size={28} color="white" />
                        </View>
                        <Text fontSize="$2" fontWeight="600" color="$gray11">
                          Driver
                        </Text>
                        <Text fontSize="$1" color="$gray8">
                          {order.driverName}
                        </Text>
                      </YStack>

                      <View style={{ alignItems: 'center' }}>
                        <View
                          style={{
                            width: 80,
                            height: 3,
                            backgroundColor: '#7c3aed',
                            borderRadius: 2,
                            marginBottom: 8,
                          }}
                        />
                        <Text fontSize="$1" color="$gray8" fontWeight="600">
                          {estimatedTime > 0 ? `${estimatedTime} mins` : 'Arriving!'}
                        </Text>
                      </View>

                      <YStack ai="center" gap="$2">
                        <View
                          style={{
                            backgroundColor: '#10b981',
                            borderRadius: 16,
                            padding: 12,
                            borderWidth: 2,
                            borderColor: 'white',
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.2,
                            shadowRadius: 4,
                          }}
                        >
                          <Ionicons name="location" size={28} color="white" />
                        </View>
                        <Text fontSize="$2" fontWeight="600" color="$gray11">
                          Destination
                        </Text>
                        <Text fontSize="$1" color="$gray8" textAlign="center">
                          Customer
                        </Text>
                      </YStack>
                    </XStack>

                    <View
                      style={{
                        backgroundColor: '#f3f4f6',
                        borderRadius: 8,
                        paddingHorizontal: 16,
                        paddingVertical: 8,
                      }}
                    >
                      <Text fontSize="$3" color="$gray11" fontWeight="600">
                        📍 Tap to view full map with live tracking
                      </Text>
                    </View>
                  </YStack>
                </View>
              </Pressable>
            </Card>
          </MotiView>

          {/* Enhanced Destination Info */}
          <MotiView
            from={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 300, duration: 500 }}
          >
            <Card
              elevate
              p="$5"
              br="$8"
              bg="white"
              borderWidth={1}
              borderColor="$gray4"
              shadowColor="$gray6"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
            >
              <YStack gap="$4">
                <XStack ai="center" gap="$3">
                  <LinearGradient
                    colors={['#3b82f6', '#2563eb']}
                    style={{
                      borderRadius: 12,
                      padding: 12,
                    }}
                  >
                    <Ionicons name="location" size={24} color="white" />
                  </LinearGradient>
                  <H3 color="$gray12" fontWeight="800">Delivery Details</H3>
                </XStack>

                <YStack gap="$3">
                  <XStack ai="flex-start" gap="$3">
                    <View
                      style={{
                        backgroundColor: '#f3f4f6',
                        borderRadius: 8,
                        padding: 8,
                      }}
                    >
                      <Ionicons name="home" size={16} color="#6b7280" />
                    </View>
                    <YStack flex={1}>
                      <Text color="$gray8" fontSize="$2" fontWeight="600">DESTINATION</Text>
                      <Text color="$gray12" fontSize="$3" fontWeight="500">
                        {order.address?.address || 'Address not provided'}
                      </Text>
                    </YStack>
                  </XStack>

                  <XStack ai="center" gap="$3">
                    <View
                      style={{
                        backgroundColor: '#f3f4f6',
                        borderRadius: 8,
                        padding: 8,
                      }}
                    >
                      <Ionicons name="person" size={16} color="#6b7280" />
                    </View>
                    <YStack flex={1}>
                      <Text color="$gray8" fontSize="$2" fontWeight="600">CUSTOMER</Text>
                      <Text color="$gray12" fontSize="$3" fontWeight="500">
                        {order.phone}
                      </Text>
                    </YStack>

                    <Button
                      size="$3"
                      circular
                      bg="$green9"
                      color="white"
                      icon={<Ionicons name="call" size={18} color="white" />}
                      onPress={() => Linking.openURL(`tel:${order.phone}`)}
                      pressStyle={{ scale: 0.95 }}
                    />
                  </XStack>

                  <XStack ai="center" gap="$3">
                    <View
                      style={{
                        backgroundColor: '#f3f4f6',
                        borderRadius: 8,
                        padding: 8,
                      }}
                    >
                      <Ionicons name="car-sport" size={16} color="#6b7280" />
                    </View>
                    <YStack flex={1}>
                      <Text color="$gray8" fontSize="$2" fontWeight="600">DRIVER</Text>
                      <Text color="$gray12" fontSize="$3" fontWeight="500">
                        {order.driverName} • {order.driverPhone}
                      </Text>
                    </YStack>

                    <Button
                      size="$3"
                      circular
                      bg="$blue9"
                      color="white"
                      icon={<Ionicons name="call" size={18} color="white" />}
                      onPress={() => Linking.openURL(`tel:${order.driverPhone}`)}
                      pressStyle={{ scale: 0.95 }}
                    />
                  </XStack>
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Enhanced Timeline */}
          <MotiView
            from={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 400, duration: 500 }}
          >
            <Card
              elevate
              p="$5"
              br="$8"
              bg="white"
              borderWidth={1}
              borderColor="$gray4"
              shadowColor="$gray6"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
            >
              <YStack gap="$4">
                <XStack ai="center" gap="$3">
                  <LinearGradient
                    colors={['#10b981', '#059669']}
                    style={{
                      borderRadius: 12,
                      padding: 12,
                    }}
                  >
                    <Ionicons name="time" size={24} color="white" />
                  </LinearGradient>
                  <H3 color="$gray12" fontWeight="800">Delivery Progress</H3>
                </XStack>

                <XStack jc="space-between" ai="center" px="$2">
                  {[
                    { stage: 'Preparing', icon: 'restaurant', color: '#f59e0b' },
                    { stage: 'On the Way', icon: 'car', color: '#f97316' },
                    { stage: 'Delivered', icon: 'checkmark-done', color: '#10b981' }
                  ].map((item, index) => {
                    const isActive = order.status === item.stage;
                    const isCompleted =
                      (order.status === 'On the Way' && item.stage === 'Preparing') ||
                      (order.status === 'Delivered' && item.stage !== 'Delivered');
                    const isActiveOrCompleted = isActive || isCompleted;

                    return (
                      <YStack ai="center" key={item.stage} flex={1}>
                        <MotiView
                          from={{ scale: 0.8, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          transition={{ delay: 500 + index * 200, type: 'spring', damping: 15 }}
                        >
                          <View
                            style={{
                              backgroundColor: isActiveOrCompleted ? item.color : '#e5e7eb',
                              borderRadius: 20,
                              padding: 16,
                              borderWidth: 3,
                              borderColor: isActive ? 'white' : 'transparent',
                              shadowColor: isActiveOrCompleted ? item.color : 'transparent',
                              shadowOffset: { width: 0, height: 4 },
                              shadowOpacity: 0.3,
                              shadowRadius: 8,
                            }}
                          >
                            <Ionicons
                              name={item.icon as any}
                              size={24}
                              color={isActiveOrCompleted ? 'white' : '#9ca3af'}
                            />
                          </View>
                        </MotiView>

                        <Text
                          fontSize="$2"
                          mt="$2"
                          fontWeight={isActive ? "800" : "600"}
                          color={isActiveOrCompleted ? "$gray12" : "$gray8"}
                          textAlign="center"
                        >
                          {item.stage}
                        </Text>

                        {isActive ? (
                          <MotiView
                            from={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 800, duration: 400 }}
                          >
                            <View
                              style={{
                                backgroundColor: item.color,
                                borderRadius: 4,
                                paddingHorizontal: 8,
                                paddingVertical: 2,
                                marginTop: 4,
                              }}
                            >
                              <Text color="white" fontSize="$1" fontWeight="700">
                                ACTIVE
                              </Text>
                            </View>
                          </MotiView>
                        ) : null}
                      </YStack>
                    );
                  })}
                </XStack>

                {/* Progress Bar */}
                <View
                  style={{
                    height: 4,
                    backgroundColor: '#e5e7eb',
                    borderRadius: 2,
                    marginTop: 8,
                    overflow: 'hidden',
                  }}
                >
                  <MotiView
                    from={{ width: '0%' }}
                    animate={{
                      width: order.status === 'Preparing' ? '33%' :
                             order.status === 'On the Way' ? '66%' :
                             order.status === 'Delivered' ? '100%' : '0%'
                    }}
                    transition={{ delay: 1000, duration: 1000 }}
                    style={{
                      height: '100%',
                      backgroundColor: '#10b981',
                      borderRadius: 2,
                    }}
                  />
                </View>
              </YStack>
            </Card>
          </MotiView>
        </YStack>
      </ScrollView>
    </>
  );
}
