/* import { ComponentProps, forwardRef } from 'react';
import { TamaguiElement } from 'tamagui';

import { Button as TButton } from '../tamagui.config';

type ButtonProps = {
  title: string;
} & ComponentProps<typeof TButton>;

export const Button = forwardRef<TamaguiElement, ButtonProps>(({ title, ...tButtonProps }, ref) => {
  return (
    <TButton {...tButtonProps} ref={ref}>
      {title}
    </TButton>
  );
});

Button.displayName = 'Button';
 */

import { ComponentProps, forwardRef, ReactNode } from 'react';
import { TamaguiElement, Button as TButton } from 'tamagui';

type ButtonProps = {
  title?: string;
  children?: ReactNode;
} & ComponentProps<typeof TButton>;

export const Button = forwardRef<TamaguiElement, ButtonProps>(
  ({ title, children, ...tButtonProps }, ref) => {
    return (
      <TButton {...tButtonProps} ref={ref}>
        {children ?? title}
      </TButton>
    );
  }
);

Button.displayName = 'Button';
